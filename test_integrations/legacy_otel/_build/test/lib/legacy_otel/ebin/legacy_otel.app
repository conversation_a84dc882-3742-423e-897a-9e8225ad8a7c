{application,legacy_otel,
             [{modules,['Elixir.LegacyOtel']},
              {optional_applications,[jason]},
              {applications,[kernel,stdlib,elixir,logger,sentry,hackney,jason,
                             opentelemetry,opentelemetry_api,
                             opentelemetry_exporter,
                             opentelemetry_semantic_conventions]},
              {description,"legacy_otel"},
              {registered,[]},
              {vsn,"0.1.0"}]}.
