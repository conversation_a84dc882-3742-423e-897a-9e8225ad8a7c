{<<"app">>,<<"hackney">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"simple HTTP client">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"MAINTAINERS">>,<<"NEWS.md">>,<<"NOTICE">>,<<"README.md">>,
  <<"include">>,<<"include/hackney.hrl">>,<<"include/hackney_lib.hrl">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"src">>,<<"src/hackney.app.src">>,
  <<"src/hackney.erl">>,<<"src/hackney_app.erl">>,<<"src/hackney_bstr.erl">>,
  <<"src/hackney_connect.erl">>,<<"src/hackney_connection.erl">>,
  <<"src/hackney_connections.erl">>,<<"src/hackney_cookie.erl">>,
  <<"src/hackney_date.erl">>,<<"src/hackney_happy.erl">>,
  <<"src/hackney_headers.erl">>,<<"src/hackney_headers_new.erl">>,
  <<"src/hackney_http.erl">>,<<"src/hackney_http_connect.erl">>,
  <<"src/hackney_internal.hrl">>,<<"src/hackney_local_tcp.erl">>,
  <<"src/hackney_manager.erl">>,<<"src/hackney_methods.hrl">>,
  <<"src/hackney_metrics.erl">>,<<"src/hackney_multipart.erl">>,
  <<"src/hackney_pool.erl">>,<<"src/hackney_pool_handler.erl">>,
  <<"src/hackney_request.erl">>,<<"src/hackney_response.erl">>,
  <<"src/hackney_socks5.erl">>,<<"src/hackney_ssl.erl">>,
  <<"src/hackney_ssl_certificate.erl">>,<<"src/hackney_stream.erl">>,
  <<"src/hackney_sup.erl">>,<<"src/hackney_tcp.erl">>,
  <<"src/hackney_trace.erl">>,<<"src/hackney_url.erl">>,
  <<"src/hackney_util.erl">>,<<"src/libs">>,<<"src/libs/hackney_cidr.erl">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"links">>,[{<<"Github">>,<<"https://github.com/benoitc/hackney">>}]}.
{<<"name">>,<<"hackney">>}.
{<<"requirements">>,
 [{<<"certifi">>,
   [{<<"app">>,<<"certifi">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>2.15.0">>}]},
  {<<"idna">>,
   [{<<"app">>,<<"idna">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>6.1.0">>}]},
  {<<"metrics">>,
   [{<<"app">>,<<"metrics">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>1.0.0">>}]},
  {<<"mimerl">>,
   [{<<"app">>,<<"mimerl">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>1.4">>}]},
  {<<"parse_trans">>,
   [{<<"app">>,<<"parse_trans">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"3.4.1">>}]},
  {<<"ssl_verify_fun">>,
   [{<<"app">>,<<"ssl_verify_fun">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>1.1.0">>}]},
  {<<"unicode_util_compat">>,
   [{<<"app">>,<<"unicode_util_compat">>},
    {<<"optional">>,false},
    {<<"requirement">>,<<"~>0.7.1">>}]}]}.
{<<"version">>,<<"1.25.0">>}.
